import React, { useState, useEffect } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import "./App.css";

const YourComponent = () => {
  const [data, setData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 3;

  useEffect(() => {
    // Fetch data from JSONPlaceholder (replace with your actual API endpoint)
    fetch(
      `https://jsonplaceholder.typicode.com/posts?_page=${currentPage}&_limit=${itemsPerPage}`
    )
      .then((response) => response.json())
      .then((newData) => {
        setData((prevData) => [...prevData, ...newData]);
      })
      .catch((error) => console.error("Error fetching data:", error));
  }, [currentPage]);

  const settings = {
    infinite: true,
    speed: 500,
    slidesToShow: Math.min(3, data.length),
    slidesToScroll: 1,
    focusOnSelect: true,
    cssEase: "linear",
    touchMove: true,
    draggable: true,
    dots: false,
    beforeChange: (oldIndex, newIndex) => {
      console.log("first124562", newIndex, data?.length, data?.length - 3);
      if (newIndex === data.length - 3) {
        setCurrentPage((prevPage) => prevPage + 1);
      }
    },
  };
  console.log("firstData", data);
  return (
    <div className="slider-wrap">
      <Slider {...settings}>
        {data.map((item) => (
          <div key={item.id}>
            {/* Render your slide content here */}
            <h3>{item.title}</h3>
            <p>{item.body}</p>
          </div>
        ))}
      </Slider>
    </div>
  );
};

export default YourComponent;
