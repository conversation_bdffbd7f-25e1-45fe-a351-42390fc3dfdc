import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { flexRender } from '@tanstack/react-table';

const DraggableTableRow = ({ row, getColumnStyle }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: row.original.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    backgroundColor: isDragging ? '#f0f8ff' : 'transparent',
    zIndex: isDragging ? 1000 : 'auto',
  };

  return (
    <tr
      ref={setNodeRef}
      style={style}
      className={`draggable-row ${isDragging ? 'dragging' : ''}`}
      {...attributes}
    >
      {row.getVisibleCells().map((cell, index) => {
        const columnStyle = getColumnStyle(cell.column);
        const isFirstCell = index === 0;
        
        return (
          <td 
            key={cell.id} 
            style={columnStyle}
            className={`table-cell ${columnStyle.position === 'sticky' ? 'sticky-cell' : ''}`}
          >
            {isFirstCell && (
              <div className="row-drag-handle" {...listeners}>
                <span className="drag-icon">⋮⋮</span>
              </div>
            )}
            <div className="cell-content">
              {flexRender(
                cell.column.columnDef.cell,
                cell.getContext()
              )}
            </div>
          </td>
        );
      })}
    </tr>
  );
};

export default DraggableTableRow;
