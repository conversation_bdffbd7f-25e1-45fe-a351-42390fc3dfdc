import React, { useState } from 'react';
import './DragDropGuide.css';

const DragDropGuide = () => {
  const [activeSection, setActiveSection] = useState('overview');

  const sections = {
    overview: {
      title: 'Drag & Drop + Sticky Columns Overview',
      content: (
        <div className="guide-content">
          <h3>🎯 Advanced Table Features</h3>
          <p>
            This implementation combines Tanstack Table with @dnd-kit for drag & drop functionality 
            and CSS sticky positioning for multiple sticky columns.
          </p>
          
          <h4>Key Features Implemented:</h4>
          <ul>
            <li>🔄 <strong>Column Reordering</strong> - Drag column headers to reorder</li>
            <li>📋 <strong>Row Reordering</strong> - Drag rows to change their position</li>
            <li>📌 <strong>Multiple Sticky Columns</strong> - Pin multiple columns to left/right</li>
            <li>🎨 <strong>Visual Feedback</strong> - Smooth animations and hover effects</li>
            <li>📱 <strong>Touch Support</strong> - Works on mobile devices</li>
            <li>♿ <strong>Accessibility</strong> - Keyboard navigation support</li>
          </ul>

          <h4>Libraries Used:</h4>
          <div className="library-grid">
            <div className="library-card">
              <h5>@tanstack/react-table</h5>
              <p>Core table functionality and state management</p>
            </div>
            <div className="library-card">
              <h5>@dnd-kit/core</h5>
              <p>Drag and drop context and sensors</p>
            </div>
            <div className="library-card">
              <h5>@dnd-kit/sortable</h5>
              <p>Sortable components and strategies</p>
            </div>
            <div className="library-card">
              <h5>@dnd-kit/utilities</h5>
              <p>CSS transform utilities</p>
            </div>
          </div>
        </div>
      )
    },
    dragdrop: {
      title: 'Drag & Drop Implementation',
      content: (
        <div className="guide-content">
          <h3>🔄 Drag & Drop Setup</h3>
          
          <h4>1. Installation</h4>
          <div className="code-block">
            <code>npm install @dnd-kit/core @dnd-kit/sortable @dnd-kit/utilities</code>
          </div>

          <h4>2. Basic DndContext Setup</h4>
          <div className="code-block">
            <pre>{`import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';

const sensors = useSensors(
  useSensor(PointerSensor),
  useSensor(KeyboardSensor, {
    coordinateGetter: sortableKeyboardCoordinates,
  })
);

<DndContext
  sensors={sensors}
  collisionDetection={closestCenter}
  onDragEnd={handleDragEnd}
>
  {/* Your sortable content */}
</DndContext>`}</pre>
          </div>

          <h4>3. Column Drag & Drop</h4>
          <div className="code-block">
            <pre>{`// Column reordering handler
const handleColumnDragEnd = (event) => {
  const { active, over } = event;
  
  if (active.id !== over?.id) {
    const oldIndex = columnOrder.indexOf(active.id);
    const newIndex = columnOrder.indexOf(over.id);
    
    if (oldIndex !== -1 && newIndex !== -1) {
      setColumnOrder(arrayMove(columnOrder, oldIndex, newIndex));
    }
  }
};

// Use horizontal sorting strategy
<SortableContext
  items={table.getAllColumns().map(c => c.id)}
  strategy={horizontalListSortingStrategy}
>
  {/* Column headers */}
</SortableContext>`}</pre>
          </div>

          <h4>4. Row Drag & Drop</h4>
          <div className="code-block">
            <pre>{`// Row reordering handler
const handleRowDragEnd = (event) => {
  const { active, over } = event;
  
  if (active.id !== over?.id) {
    setData((items) => {
      const oldIndex = items.findIndex(item => item.id === active.id);
      const newIndex = items.findIndex(item => item.id === over.id);
      
      return arrayMove(items, oldIndex, newIndex);
    });
  }
};

// Use vertical sorting strategy
<SortableContext
  items={table.getRowModel().rows.map(row => row.original.id)}
  strategy={verticalListSortingStrategy}
>
  {/* Table rows */}
</SortableContext>`}</pre>
          </div>
        </div>
      )
    },
    sticky: {
      title: 'Sticky Columns Implementation',
      content: (
        <div className="guide-content">
          <h3>📌 Multiple Sticky Columns</h3>
          
          <h4>1. Column Configuration</h4>
          <div className="code-block">
            <pre>{`const columns = [
  {
    accessorKey: 'id',
    header: 'ID',
    meta: {
      sticky: 'left',
      stickyOffset: 0, // First sticky column
    },
  },
  {
    accessorKey: 'name',
    header: 'Name',
    meta: {
      sticky: 'left',
      stickyOffset: 80, // Offset by previous column width
    },
  },
  // ... other columns
  {
    id: 'actions',
    header: 'Actions',
    meta: {
      sticky: 'right',
      stickyOffset: 0, // Right-aligned sticky
    },
  }
]`}</pre>
          </div>

          <h4>2. Dynamic Sticky Styling</h4>
          <div className="code-block">
            <pre>{`const getColumnStyle = (column) => {
  const columnMeta = column.columnDef.meta;
  if (!columnMeta?.sticky || !stickyColumns.includes(column.id)) {
    return {};
  }

  const style = {
    position: 'sticky',
    zIndex: 10,
    backgroundColor: 'white',
  };

  if (columnMeta.sticky === 'left') {
    style.left = \`\${columnMeta.stickyOffset}px\`;
    style.borderRight = '2px solid #e0e0e0';
  } else if (columnMeta.sticky === 'right') {
    style.right = \`\${columnMeta.stickyOffset}px\`;
    style.borderLeft = '2px solid #e0e0e0';
  }

  return style;
};`}</pre>
          </div>

          <h4>3. Sticky Column Management</h4>
          <div className="code-block">
            <pre>{`const [stickyColumns, setStickyColumns] = useState(['id', 'name']);

const toggleStickyColumn = (columnId) => {
  setStickyColumns(prev => {
    if (prev.includes(columnId)) {
      return prev.filter(id => id !== columnId);
    } else {
      return [...prev, columnId];
    }
  });
};

// UI for toggling sticky columns
{table.getAllColumns().map(column => (
  <label key={column.id}>
    <input
      type="checkbox"
      checked={stickyColumns.includes(column.id)}
      onChange={() => toggleStickyColumn(column.id)}
    />
    {column.columnDef.header}
  </label>
))}`}</pre>
          </div>
        </div>
      )
    },
    components: {
      title: 'Component Architecture',
      content: (
        <div className="guide-content">
          <h3>🏗️ Component Structure</h3>
          
          <h4>Main Components:</h4>
          <div className="component-list">
            <div className="component-item">
              <h5>DragDropStickyTable</h5>
              <p>Main container component that manages state and coordinates drag & drop contexts</p>
            </div>
            <div className="component-item">
              <h5>DraggableColumnHeader</h5>
              <p>Individual column header with drag handle and sorting functionality</p>
            </div>
            <div className="component-item">
              <h5>DraggableTableRow</h5>
              <p>Table row with drag handle and sticky cell support</p>
            </div>
          </div>

          <h4>DraggableColumnHeader Component:</h4>
          <div className="code-block">
            <pre>{`const DraggableColumnHeader = ({ header, style }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: header.id });

  return (
    <th ref={setNodeRef} style={{...style, transform, transition}}>
      <div className="header-content">
        <div className="drag-handle" {...listeners} {...attributes}>
          ⋮⋮
        </div>
        <div className="header-text">
          {header.column.columnDef.header}
        </div>
      </div>
    </th>
  );
};`}</pre>
          </div>

          <h4>DraggableTableRow Component:</h4>
          <div className="code-block">
            <pre>{`const DraggableTableRow = ({ row, getColumnStyle }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: row.original.id });

  return (
    <tr ref={setNodeRef} style={{transform, transition}}>
      {row.getVisibleCells().map((cell, index) => (
        <td key={cell.id} style={getColumnStyle(cell.column)}>
          {index === 0 && (
            <div className="row-drag-handle" {...listeners} {...attributes}>
              ⋮⋮
            </div>
          )}
          {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </td>
      ))}
    </tr>
  );
};`}</pre>
          </div>
        </div>
      )
    },
    styling: {
      title: 'Styling & UX',
      content: (
        <div className="guide-content">
          <h3>🎨 Styling Considerations</h3>
          
          <h4>Drag Visual Feedback:</h4>
          <div className="code-block">
            <pre>{`.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
  box-shadow: 0 8px 25px rgba(0,0,0,0.3);
  z-index: 1000;
}

.drag-handle {
  cursor: grab;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.drag-handle:active {
  cursor: grabbing;
}

tr:hover .drag-handle {
  opacity: 1;
}`}</pre>
          </div>

          <h4>Sticky Column Styling:</h4>
          <div className="code-block">
            <pre>{`.sticky-cell {
  position: sticky;
  background: white;
  z-index: 10;
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.sticky-left {
  border-right: 2px solid #e0e0e0;
}

.sticky-right {
  border-left: 2px solid #e0e0e0;
}`}</pre>
          </div>

          <h4>Performance Optimizations:</h4>
          <ul>
            <li>Use <code>transform</code> instead of changing <code>top/left</code> for smooth animations</li>
            <li>Implement <code>will-change: transform</code> for elements being dragged</li>
            <li>Use <code>pointer-events: none</code> on drag overlays</li>
            <li>Debounce expensive operations during drag</li>
            <li>Use <code>React.memo</code> for row components to prevent unnecessary re-renders</li>
          </ul>

          <h4>Accessibility Features:</h4>
          <ul>
            <li>Keyboard navigation support with arrow keys</li>
            <li>Screen reader announcements for drag operations</li>
            <li>Focus management during drag operations</li>
            <li>ARIA labels for drag handles</li>
            <li>High contrast mode support</li>
          </ul>
        </div>
      )
    }
  };

  return (
    <div className="dragdrop-guide">
      <div className="guide-header">
        <h1>🎯 Drag & Drop + Sticky Columns Guide</h1>
        <p>Advanced table interactions with smooth UX</p>
      </div>

      <div className="guide-layout">
        <nav className="guide-nav">
          {Object.entries(sections).map(([key, section]) => (
            <button
              key={key}
              className={`nav-item ${activeSection === key ? 'active' : ''}`}
              onClick={() => setActiveSection(key)}
            >
              {section.title}
            </button>
          ))}
        </nav>

        <main className="guide-main">
          <div className="section-content">
            <h2>{sections[activeSection].title}</h2>
            {sections[activeSection].content}
          </div>
        </main>
      </div>
    </div>
  );
};

export default DragDropGuide;
