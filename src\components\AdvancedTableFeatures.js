import React, { useState, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getGroupedRowModel,
  getExpandedRowModel,
  flexRender,
} from '@tanstack/react-table';
import { sampleOrders } from '../data/dummyData';
import './AdvancedTableFeatures.css';

const AdvancedTableFeatures = () => {
  const [data, setData] = useState(sampleOrders);
  const [sorting, setSorting] = useState([]);
  const [filtering, setFiltering] = useState('');
  const [grouping, setGrouping] = useState([]);
  const [expanded, setExpanded] = useState({});
  const [rowSelection, setRowSelection] = useState({});

  const columns = useMemo(
    () => [
      // Row selection column
      {
        id: 'select',
        header: ({ table }) => (
          <input
            type="checkbox"
            checked={table.getIsAllRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
            className="checkbox"
          />
        ),
        cell: ({ row }) => (
          <input
            type="checkbox"
            checked={row.getIsSelected()}
            onChange={row.getToggleSelectedHandler()}
            className="checkbox"
          />
        ),
        enableSorting: false,
        enableGrouping: false,
      },
      {
        accessorKey: 'id',
        header: 'Order ID',
        cell: ({ getValue }) => (
          <span className="order-id">{getValue()}</span>
        ),
        enableGrouping: false,
      },
      {
        accessorKey: 'customerName',
        header: 'Customer',
        cell: ({ getValue }) => (
          <span className="customer-name">{getValue()}</span>
        ),
      },
      {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ getValue }) => (
          <span className={`status-badge ${getValue().toLowerCase()}`}>
            {getValue()}
          </span>
        ),
        enableGrouping: true,
      },
      {
        accessorKey: 'orderDate',
        header: 'Order Date',
        cell: ({ getValue }) => (
          <span className="date-cell">
            {new Date(getValue()).toLocaleDateString()}
          </span>
        ),
      },
      {
        accessorKey: 'total',
        header: 'Total',
        cell: ({ getValue }) => (
          <span className="total-cell">
            ${getValue().toFixed(2)}
          </span>
        ),
        aggregationFn: 'sum',
        aggregatedCell: ({ getValue }) => (
          <span className="aggregated-total">
            Total: ${getValue().toFixed(2)}
          </span>
        ),
      },
      {
        accessorKey: 'items',
        header: 'Items',
        cell: ({ getValue }) => (
          <span className="items-count">{getValue()}</span>
        ),
        aggregationFn: 'sum',
        aggregatedCell: ({ getValue }) => (
          <span className="aggregated-items">
            Total Items: {getValue()}
          </span>
        ),
      },
      {
        accessorKey: 'paymentMethod',
        header: 'Payment',
        cell: ({ getValue }) => (
          <span className={`payment-method ${getValue().toLowerCase().replace(' ', '-')}`}>
            {getValue()}
          </span>
        ),
        enableGrouping: true,
      },
      {
        accessorKey: 'priority',
        header: 'Priority',
        cell: ({ getValue }) => (
          <span className={`priority-badge ${getValue().toLowerCase()}`}>
            {getValue()}
          </span>
        ),
        enableGrouping: true,
      },
      {
        id: 'expand',
        header: 'Details',
        cell: ({ row }) => (
          <button
            onClick={row.getToggleExpandedHandler()}
            className="expand-btn"
          >
            {row.getIsExpanded() ? '▼' : '▶'}
          </button>
        ),
        enableSorting: false,
        enableGrouping: false,
      },
    ],
    []
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getGroupedRowModel: getGroupedRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    state: {
      sorting,
      globalFilter: filtering,
      grouping,
      expanded,
      rowSelection,
    },
    onSortingChange: setSorting,
    onGlobalFilterChange: setFiltering,
    onGroupingChange: setGrouping,
    onExpandedChange: setExpanded,
    onRowSelectionChange: setRowSelection,
    enableRowSelection: true,
    enableGrouping: true,
    enableExpanding: true,
    getSubRows: (row) => row.subRows,
    initialState: {
      pagination: {
        pageSize: 15,
      },
    },
  });

  const handleBulkAction = (action) => {
    const selectedRows = table.getSelectedRowModel().rows;
    const selectedIds = selectedRows.map(row => row.original.id);
    
    switch (action) {
      case 'delete':
        if (window.confirm(`Delete ${selectedIds.length} selected orders?`)) {
          setData(prev => prev.filter(order => !selectedIds.includes(order.id)));
          setRowSelection({});
        }
        break;
      case 'export':
        console.log('Exporting orders:', selectedIds);
        alert(`Exporting ${selectedIds.length} orders`);
        break;
      case 'update-status':
        const newStatus = prompt('Enter new status:');
        if (newStatus) {
          setData(prev => prev.map(order => 
            selectedIds.includes(order.id) 
              ? { ...order, status: newStatus }
              : order
          ));
          setRowSelection({});
        }
        break;
      default:
        break;
    }
  };

  const renderSubComponent = ({ row }) => (
    <div className="sub-component">
      <h4>Order Details for {row.original.id}</h4>
      <div className="details-grid">
        <div className="detail-item">
          <strong>Customer Email:</strong> {row.original.customerEmail}
        </div>
        <div className="detail-item">
          <strong>Delivery Date:</strong> {new Date(row.original.deliveryDate).toLocaleDateString()}
        </div>
        <div className="detail-item">
          <strong>Shipping Address:</strong>
          <div className="address">
            {row.original.shippingAddress.street}<br/>
            {row.original.shippingAddress.city}, {row.original.shippingAddress.state} {row.original.shippingAddress.zipCode}<br/>
            {row.original.shippingAddress.country}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="advanced-table-container">
      <div className="table-header">
        <h2>Advanced Table Features</h2>
        <p>Grouping, Row Selection, Expansion, and Aggregation</p>
      </div>

      <div className="advanced-controls">
        <div className="search-group">
          <input
            type="text"
            placeholder="Search orders..."
            value={filtering}
            onChange={(e) => setFiltering(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="grouping-controls">
          <label>Group by:</label>
          <select 
            value={grouping[0] || ''}
            onChange={(e) => setGrouping(e.target.value ? [e.target.value] : [])}
            className="group-select"
          >
            <option value="">No Grouping</option>
            <option value="status">Status</option>
            <option value="paymentMethod">Payment Method</option>
            <option value="priority">Priority</option>
          </select>
        </div>

        <div className="bulk-actions">
          <span>Selected: {Object.keys(rowSelection).length}</span>
          {Object.keys(rowSelection).length > 0 && (
            <div className="bulk-buttons">
              <button onClick={() => handleBulkAction('delete')} className="bulk-btn delete">
                Delete
              </button>
              <button onClick={() => handleBulkAction('export')} className="bulk-btn export">
                Export
              </button>
              <button onClick={() => handleBulkAction('update-status')} className="bulk-btn update">
                Update Status
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="table-wrapper">
        <table className="advanced-table">
          <thead>
            {table.getHeaderGroups().map(headerGroup => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <th key={header.id}>
                    <div className="header-content">
                      {header.isPlaceholder ? null : (
                        <>
                          <div
                            className={header.column.getCanSort() ? 'sortable' : ''}
                            onClick={header.column.getToggleSortingHandler()}
                          >
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {header.column.getCanSort() && (
                              <span className="sort-indicator">
                                {{
                                  asc: ' 🔼',
                                  desc: ' 🔽',
                                }[header.column.getIsSorted()] ?? ' ↕️'}
                              </span>
                            )}
                          </div>
                          {header.column.getCanGroup() && (
                            <button
                              onClick={header.column.getToggleGroupingHandler()}
                              className={`group-btn ${header.column.getIsGrouped() ? 'grouped' : ''}`}
                            >
                              📊
                            </button>
                          )}
                        </>
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map(row => (
              <React.Fragment key={row.id}>
                <tr className={`table-row ${row.getIsSelected() ? 'selected' : ''}`}>
                  {row.getVisibleCells().map(cell => (
                    <td key={cell.id} style={{ paddingLeft: `${row.depth * 20}px` }}>
                      {cell.getIsGrouped() ? (
                        <div className="grouped-cell">
                          <button
                            onClick={row.getToggleExpandedHandler()}
                            className="expand-group-btn"
                          >
                            {row.getIsExpanded() ? '👇' : '👉'}
                          </button>
                          {flexRender(cell.column.columnDef.cell, cell.getContext())} 
                          ({row.subRows.length})
                        </div>
                      ) : cell.getIsAggregated() ? (
                        flexRender(
                          cell.column.columnDef.aggregatedCell ?? cell.column.columnDef.cell,
                          cell.getContext()
                        )
                      ) : cell.getIsPlaceholder() ? null : (
                        flexRender(cell.column.columnDef.cell, cell.getContext())
                      )}
                    </td>
                  ))}
                </tr>
                {row.getIsExpanded() && !row.getIsGrouped() && (
                  <tr>
                    <td colSpan={row.getVisibleCells().length}>
                      {renderSubComponent({ row })}
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      <div className="table-pagination">
        <div className="pagination-info">
          <span>
            Showing {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} to{' '}
            {Math.min(
              (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
              table.getFilteredRowModel().rows.length
            )}{' '}
            of {table.getFilteredRowModel().rows.length} entries
          </span>
        </div>
        
        <div className="pagination-controls">
          <button
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
            className="pagination-btn"
          >
            {'<<'}
          </button>
          <button
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="pagination-btn"
          >
            {'<'}
          </button>
          
          <span className="page-info">
            Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
          </span>
          
          <button
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="pagination-btn"
          >
            {'>'}
          </button>
          <button
            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage()}
            className="pagination-btn"
          >
            {'>>'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdvancedTableFeatures;
