import React, { useState, useTransition } from "react";

const UseTransition = () => {
  const [items, setItems] = useState(["Item 1", "Item 2", "Item 3"]);
  const [isPending, startTransition] = useTransition({
    timeoutMs: 500,
  });

  const handleItemClick = (index) => {
    startTransition(() => {
      setItems((prevItems) => {
        const updatedItems = [...prevItems];
        updatedItems.splice(index, 1);
        return updatedItems;
      });
    });
  };
  return (
    <>
      <ul>
        {items.map((item, index) => (
          <li key={index} onClick={() => handleItemClick(index)}>
            {item}
          </li>
        ))}
        {isPending && <div>Loading....</div>}
      </ul>
    </>
  );
};

export default UseTransition;
