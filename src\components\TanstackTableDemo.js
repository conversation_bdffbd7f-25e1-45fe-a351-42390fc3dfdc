import React, { useState, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  flexRender,
} from '@tanstack/react-table';
import { sampleUsers } from '../data/dummyData';
import './TanstackTableDemo.css';

const TanstackTableDemo = () => {
  const [data, setData] = useState(sampleUsers);
  const [sorting, setSorting] = useState([]);
  const [filtering, setFiltering] = useState('');
  const [columnFilters, setColumnFilters] = useState([]);

  // Define table columns
  const columns = useMemo(
    () => [
      {
        accessorKey: 'id',
        header: 'ID',
        size: 60,
        cell: ({ getValue }) => (
          <span className="id-cell">{getValue()}</span>
        ),
      },
      {
        accessorKey: 'avatar',
        header: 'Avatar',
        size: 80,
        cell: ({ getValue }) => (
          <img 
            src={getValue()} 
            alt="Avatar" 
            className="avatar-image"
          />
        ),
        enableSorting: false,
        enableColumnFilter: false,
      },
      {
        accessorKey: 'firstName',
        header: 'First Name',
        cell: ({ getValue }) => (
          <span className="name-cell">{getValue()}</span>
        ),
      },
      {
        accessorKey: 'lastName',
        header: 'Last Name',
        cell: ({ getValue }) => (
          <span className="name-cell">{getValue()}</span>
        ),
      },
      {
        accessorKey: 'email',
        header: 'Email',
        cell: ({ getValue }) => (
          <a href={`mailto:${getValue()}`} className="email-cell">
            {getValue()}
          </a>
        ),
      },
      {
        accessorKey: 'department',
        header: 'Department',
        cell: ({ getValue }) => (
          <span className={`department-badge ${getValue().toLowerCase()}`}>
            {getValue()}
          </span>
        ),
        filterFn: 'includesString',
      },
      {
        accessorKey: 'position',
        header: 'Position',
        cell: ({ getValue }) => (
          <span className="position-cell">{getValue()}</span>
        ),
      },
      {
        accessorKey: 'salary',
        header: 'Salary',
        cell: ({ getValue }) => (
          <span className="salary-cell">
            ${getValue().toLocaleString()}
          </span>
        ),
        sortingFn: 'basic',
      },
      {
        accessorKey: 'age',
        header: 'Age',
        size: 60,
        cell: ({ getValue }) => (
          <span className="age-cell">{getValue()}</span>
        ),
      },
      {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ getValue }) => (
          <span className={`status-badge ${getValue().toLowerCase().replace(' ', '-')}`}>
            {getValue()}
          </span>
        ),
        filterFn: 'equals',
      },
      {
        accessorKey: 'performance',
        header: 'Performance',
        cell: ({ getValue }) => (
          <div className="performance-cell">
            <div className="performance-bar">
              <div 
                className="performance-fill" 
                style={{ width: `${(getValue() / 5) * 100}%` }}
              ></div>
            </div>
            <span className="performance-text">{getValue()}/5</span>
          </div>
        ),
        sortingFn: 'basic',
      },
      {
        id: 'actions',
        header: 'Actions',
        cell: ({ row }) => (
          <div className="actions-cell">
            <button 
              className="action-btn edit-btn"
              onClick={() => handleEdit(row.original)}
            >
              Edit
            </button>
            <button 
              className="action-btn delete-btn"
              onClick={() => handleDelete(row.original.id)}
            >
              Delete
            </button>
          </div>
        ),
        enableSorting: false,
        enableColumnFilter: false,
      },
    ],
    []
  );

  // Create table instance
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      globalFilter: filtering,
      columnFilters,
    },
    onSortingChange: setSorting,
    onGlobalFilterChange: setFiltering,
    onColumnFiltersChange: setColumnFilters,
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  });

  // Action handlers
  const handleEdit = (user) => {
    alert(`Edit user: ${user.firstName} ${user.lastName}`);
  };

  const handleDelete = (userId) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      setData(prev => prev.filter(user => user.id !== userId));
    }
  };

  const handleAddUser = () => {
    const newUser = {
      id: Math.max(...data.map(u => u.id)) + 1,
      firstName: 'New',
      lastName: 'User',
      email: '<EMAIL>',
      phone: '************',
      age: 25,
      department: 'Engineering',
      position: 'Developer',
      salary: 50000,
      joinDate: new Date().toISOString().split('T')[0],
      status: 'Active',
      avatar: 'https://via.placeholder.com/40',
      performance: 3.5,
    };
    setData(prev => [...prev, newUser]);
  };

  return (
    <div className="tanstack-table-container">
      <div className="table-header">
        <h2>Employee Management System</h2>
        <p>Comprehensive Tanstack Table Demo with Full Features</p>
      </div>

      <div className="table-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder="Search all columns..."
            value={filtering}
            onChange={(e) => setFiltering(e.target.value)}
            className="global-search"
          />
        </div>
        
        <div className="action-section">
          <button className="add-btn" onClick={handleAddUser}>
            Add New User
          </button>
          <select 
            className="page-size-select"
            value={table.getState().pagination.pageSize}
            onChange={(e) => table.setPageSize(Number(e.target.value))}
          >
            {[5, 10, 20, 30, 50].map(pageSize => (
              <option key={pageSize} value={pageSize}>
                Show {pageSize}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="table-wrapper">
        <table className="custom-table">
          <thead>
            {table.getHeaderGroups().map(headerGroup => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <th 
                    key={header.id}
                    style={{ width: header.getSize() }}
                    className={header.column.getCanSort() ? 'sortable' : ''}
                  >
                    <div 
                      className="header-content"
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                      {header.column.getCanSort() && (
                        <span className="sort-indicator">
                          {{
                            asc: ' 🔼',
                            desc: ' 🔽',
                          }[header.column.getIsSorted()] ?? ' ↕️'}
                        </span>
                      )}
                    </div>
                    {header.column.getCanFilter() && (
                      <input
                        type="text"
                        placeholder={`Filter ${header.column.columnDef.header}...`}
                        value={header.column.getFilterValue() ?? ''}
                        onChange={(e) => header.column.setFilterValue(e.target.value)}
                        className="column-filter"
                      />
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map(row => (
              <tr key={row.id} className="table-row">
                {row.getVisibleCells().map(cell => (
                  <td key={cell.id}>
                    {flexRender(
                      cell.column.columnDef.cell,
                      cell.getContext()
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="table-pagination">
        <div className="pagination-info">
          <span>
            Showing {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} to{' '}
            {Math.min(
              (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
              table.getFilteredRowModel().rows.length
            )}{' '}
            of {table.getFilteredRowModel().rows.length} entries
          </span>
        </div>
        
        <div className="pagination-controls">
          <button
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
            className="pagination-btn"
          >
            {'<<'}
          </button>
          <button
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="pagination-btn"
          >
            {'<'}
          </button>
          
          <span className="page-info">
            Page{' '}
            <strong>
              {table.getState().pagination.pageIndex + 1} of{' '}
              {table.getPageCount()}
            </strong>
          </span>
          
          <button
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="pagination-btn"
          >
            {'>'}
          </button>
          <button
            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage()}
            className="pagination-btn"
          >
            {'>>'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TanstackTableDemo;
