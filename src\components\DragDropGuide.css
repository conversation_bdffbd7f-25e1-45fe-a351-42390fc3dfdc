/* Drag Drop Guide Container */
.dragdrop-guide {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.dragdrop-guide .guide-header {
  text-align: center;
  padding: 40px 20px;
  color: white;
}

.dragdrop-guide .guide-header h1 {
  font-size: 3rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.dragdrop-guide .guide-header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* Layout */
.dragdrop-guide .guide-layout {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  background: white;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
  box-shadow: 0 -10px 30px rgba(0,0,0,0.2);
  min-height: calc(100vh - 200px);
}

/* Navigation */
.dragdrop-guide .guide-nav {
  width: 280px;
  background: #f8f9fa;
  padding: 30px 0;
  border-right: 1px solid #e9ecef;
}

.dragdrop-guide .nav-item {
  display: block;
  width: 100%;
  padding: 15px 30px;
  border: none;
  background: none;
  text-align: left;
  font-size: 16px;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 4px solid transparent;
}

.dragdrop-guide .nav-item:hover {
  background: #e9ecef;
  color: #212529;
}

.dragdrop-guide .nav-item.active {
  background: #e3f2fd;
  color: #1976d2;
  border-left-color: #1976d2;
  font-weight: 600;
}

/* Main Content */
.dragdrop-guide .guide-main {
  flex: 1;
  padding: 40px;
  overflow-y: auto;
}

.dragdrop-guide .section-content h2 {
  color: #212529;
  margin-bottom: 30px;
  font-size: 2.2rem;
  border-bottom: 3px solid #1976d2;
  padding-bottom: 10px;
}

.dragdrop-guide .guide-content h3 {
  color: #495057;
  margin: 30px 0 15px 0;
  font-size: 1.5rem;
}

.dragdrop-guide .guide-content h4 {
  color: #6c757d;
  margin: 20px 0 10px 0;
  font-size: 1.2rem;
}

.dragdrop-guide .guide-content h5 {
  color: #495057;
  margin: 15px 0 8px 0;
  font-size: 1rem;
}

.dragdrop-guide .guide-content p {
  line-height: 1.6;
  color: #495057;
  margin-bottom: 15px;
}

.dragdrop-guide .guide-content ul {
  margin: 15px 0;
  padding-left: 20px;
}

.dragdrop-guide .guide-content li {
  margin-bottom: 8px;
  line-height: 1.5;
  color: #495057;
}

/* Library Grid */
.library-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.library-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #28a745;
  transition: transform 0.2s ease;
}

.library-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.library-card h5 {
  color: #28a745;
  margin-bottom: 10px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.library-card p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* Component List */
.component-list {
  margin: 20px 0;
}

.component-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  border-left: 4px solid #2196f3;
}

.component-item h5 {
  color: #2196f3;
  margin-bottom: 10px;
  font-size: 1rem;
  font-family: 'Courier New', monospace;
}

.component-item p {
  margin: 0;
  color: #495057;
  font-size: 14px;
}

/* Code Blocks */
.dragdrop-guide .code-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.dragdrop-guide .code-block code {
  font-size: 14px;
  line-height: 1.4;
}

.dragdrop-guide .code-block pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Feature Highlights */
.feature-highlight {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 10px;
  padding: 20px;
  margin: 20px 0;
  border-left: 4px solid #1976d2;
}

.feature-highlight h4 {
  color: #1976d2;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-highlight ul {
  margin: 0;
  padding-left: 20px;
}

.feature-highlight li {
  margin-bottom: 8px;
  color: #495057;
}

/* Tips and Notes */
.tip-box {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  border-left: 4px solid #f39c12;
}

.tip-box h5 {
  color: #856404;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tip-box p {
  margin: 0;
  color: #856404;
  font-size: 14px;
}

/* Warning Box */
.warning-box {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  border-left: 4px solid #dc3545;
}

.warning-box h5 {
  color: #721c24;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.warning-box p {
  margin: 0;
  color: #721c24;
  font-size: 14px;
}

/* Success Box */
.success-box {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  border-left: 4px solid #28a745;
}

.success-box h5 {
  color: #155724;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.success-box p {
  margin: 0;
  color: #155724;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dragdrop-guide .guide-layout {
    flex-direction: column;
  }
  
  .dragdrop-guide .guide-nav {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding: 20px 0;
  }
  
  .dragdrop-guide .nav-item {
    white-space: nowrap;
    border-left: none;
    border-bottom: 4px solid transparent;
    min-width: 150px;
  }
  
  .dragdrop-guide .nav-item.active {
    border-left: none;
    border-bottom-color: #1976d2;
  }
  
  .dragdrop-guide .guide-main {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .dragdrop-guide .guide-header h1 {
    font-size: 2rem;
  }
  
  .dragdrop-guide .guide-header p {
    font-size: 1rem;
  }
  
  .dragdrop-guide .section-content h2 {
    font-size: 1.8rem;
  }
  
  .library-grid {
    grid-template-columns: 1fr;
  }
  
  .dragdrop-guide .code-block {
    padding: 15px;
    font-size: 12px;
  }
  
  .dragdrop-guide .guide-main {
    padding: 15px;
  }
}

/* Scrollbar Styling */
.dragdrop-guide .guide-main::-webkit-scrollbar {
  width: 8px;
}

.dragdrop-guide .guide-main::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dragdrop-guide .guide-main::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.dragdrop-guide .guide-main::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
