// useLayoutEffect, runs synchronously after a render but before the screen is updated.

// useEffect runs asynchronously and after a render is painted to the screen.

// 1st Example

// import React, { useEffect, useLayoutEffect } from "react";

// const LayoutEffect = () => {
//   useEffect(() => {
//     console.log("First useEffect");
//   }, []);

//   useLayoutEffect(() => {
//     console.log("Second useEffect");
//   }, []);

//   // useEffect(() => {
//   //   console.log("Second useEffect");
//   // }, []);

//   useEffect(() => {
//     console.log("Third useEffect");
//   }, []);

//   return <div>LayoutEffect</div>;
// };

// export default LayoutEffect;

// Final Code useLayout Effect code

import React, { useState, useLayoutEffect, useEffect } from "react";

const LayoutEffect = () => {
  const [num, setNum] = useState(0);

  useLayoutEffect(() => {
    if (num === 0) setNum(5 + Math.random() * 50);
  }, [num]);
  console.log("🚀 ~ file: LayoutEffect.jsx ~ line 9 ~ LayoutEffect ~ num", num);

  return (
    <>
      <h2>{num}</h2>
      <button onClick={() => setNum(0)}>check</button>
    </>
  );
};

export default LayoutEffect;
