/* Advanced Table Container */
.advanced-table-container {
  max-width: 1400px;
  margin: 40px auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* Header */
.advanced-table-container .table-header {
  text-align: center;
  margin-bottom: 30px;
  color: white;
}

.advanced-table-container .table-header h2 {
  font-size: 2.2rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.advanced-table-container .table-header p {
  font-size: 1rem;
  opacity: 0.9;
}

/* Advanced Controls */
.advanced-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  flex-wrap: wrap;
  gap: 15px;
}

.search-group {
  flex: 1;
  min-width: 200px;
}

.search-input {
  width: 100%;
  padding: 12px 20px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.grouping-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
  font-weight: 500;
}

.group-select {
  padding: 10px 15px;
  border: none;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  color: white;
  font-weight: 500;
}

.bulk-buttons {
  display: flex;
  gap: 8px;
}

.bulk-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  font-weight: 600;
  font-size: 12px;
  transition: all 0.2s ease;
}

.bulk-btn.delete {
  background: #f44336;
  color: white;
}

.bulk-btn.export {
  background: #4caf50;
  color: white;
}

.bulk-btn.update {
  background: #2196f3;
  color: white;
}

.bulk-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Table Styles */
.advanced-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background: white;
}

.advanced-table thead {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.advanced-table th {
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  position: relative;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.sortable {
  cursor: pointer;
  user-select: none;
}

.sort-indicator {
  font-size: 12px;
}

.group-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 4px;
  padding: 4px 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.group-btn.grouped {
  background: rgba(255, 255, 255, 0.4);
}

.group-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.advanced-table td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
}

.table-row:hover {
  background-color: #f8f9ff;
}

.table-row.selected {
  background-color: #e3f2fd;
}

/* Checkbox Styles */
.checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* Cell Styles */
.order-id {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #666;
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
}

.customer-name {
  font-weight: 500;
  color: #333;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.pending { background: #fff3cd; color: #856404; }
.status-badge.processing { background: #cce5ff; color: #004085; }
.status-badge.shipped { background: #d4edda; color: #155724; }
.status-badge.delivered { background: #d1ecf1; color: #0c5460; }
.status-badge.cancelled { background: #f8d7da; color: #721c24; }

.date-cell {
  color: #666;
  font-size: 13px;
}

.total-cell {
  font-weight: 600;
  color: #2e7d32;
  font-size: 15px;
}

.aggregated-total {
  font-weight: 700;
  color: #1976d2;
  background: #e3f2fd;
  padding: 4px 8px;
  border-radius: 4px;
}

.items-count {
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.aggregated-items {
  font-weight: 600;
  color: #7b1fa2;
  background: #f3e5f5;
  padding: 4px 8px;
  border-radius: 4px;
}

.payment-method {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.payment-method.credit-card { background: #e8f5e8; color: #2e7d32; }
.payment-method.paypal { background: #e3f2fd; color: #1976d2; }
.payment-method.bank-transfer { background: #f3e5f5; color: #7b1fa2; }
.payment-method.cash-on-delivery { background: #fff3e0; color: #ef6c00; }

.priority-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-badge.low { background: #e8f5e8; color: #388e3c; }
.priority-badge.medium { background: #fff3e0; color: #f57c00; }
.priority-badge.high { background: #ffebee; color: #d32f2f; }
.priority-badge.urgent { background: #fce4ec; color: #c2185b; }

.expand-btn {
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 10px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.expand-btn:hover {
  background: #1976d2;
  transform: scale(1.1);
}

/* Grouped Cells */
.grouped-cell {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
}

.expand-group-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 2px;
}

/* Sub Component */
.sub-component {
  background: #f8f9fa;
  padding: 20px;
  margin: 10px 0;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.sub-component h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.detail-item {
  background: white;
  padding: 12px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.detail-item strong {
  display: block;
  margin-bottom: 5px;
  color: #666;
  font-size: 12px;
  text-transform: uppercase;
}

.address {
  font-size: 14px;
  line-height: 1.4;
  color: #333;
}

/* Pagination */
.advanced-table-container .table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  color: white;
  margin-top: 20px;
}

.advanced-table-container .pagination-info {
  font-weight: 500;
}

.advanced-table-container .pagination-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.advanced-table-container .pagination-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.advanced-table-container .pagination-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.advanced-table-container .pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.advanced-table-container .page-info {
  margin: 0 15px;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .advanced-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .bulk-actions {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .advanced-table {
    font-size: 12px;
  }
  
  .advanced-table th,
  .advanced-table td {
    padding: 8px;
  }
}
