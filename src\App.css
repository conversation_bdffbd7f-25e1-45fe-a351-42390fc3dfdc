/* .slider-container{
    max-width: 1200px;
    margin: 0px auto;
}
.slick-prev:before, .slick-next:before{
    color: black;
}
.slider-container:before{
    content: "";
    background: url("./image/black-mockup.png");
    background-size: cover;
    background-repeat: no-repeat;
    position: absolute;
    left: 0;
    right: 0;
    top: 0px;
    bottom: 0px;
    height: 716px;
    z-index: 2;
    
} */

.slider-wrap {
  max-width: 1200px;
  margin: 0px auto;
}

.slick-arrow {
  background-color: black;
}

/* App Container */
.app {
  min-height: 100vh;
  background: #f5f5f5;
}

/* Navigation */
.app-nav {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 0;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.app-title {
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.nav-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.nav-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.2);
}

.nav-btn.active {
  background: white;
  color: #667eea;
  border-color: white;
  box-shadow: 0 6px 20px rgba(0,0,0,0.2);
}

/* Main Content */
.app-main {
  min-height: calc(100vh - 100px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    text-align: center;
  }

  .app-title {
    font-size: 1.5rem;
  }

  .nav-buttons {
    justify-content: center;
  }

  .nav-btn {
    padding: 10px 20px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .nav-buttons {
    flex-direction: column;
    width: 100%;
  }

  .nav-btn {
    width: 100%;
    max-width: 300px;
  }
}
