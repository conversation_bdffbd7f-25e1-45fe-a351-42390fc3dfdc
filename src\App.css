/* .slider-container{
    max-width: 1200px;
    margin: 0px auto;
}
.slick-prev:before, .slick-next:before{
    color: black;
}
.slider-container:before{
    content: "";
    background: url("./image/black-mockup.png");
    background-size: cover;
    background-repeat: no-repeat;
    position: absolute;
    left: 0;
    right: 0;
    top: 0px;
    bottom: 0px;
    height: 716px;
    z-index: 2;
    
} */

.slider-wrap {
  max-width: 1200px;
  margin: 0px auto;
}

.slick-arrow {
  background-color: black;
}
