import React, { useState, useMemo } from "react";
import "./Memo.css";

const Memo = () => {
  const [myNum, setMyNum] = useState(0);
  const [show, setShow] = useState(false);

  const getValue = () => {
    setMyNum(myNum + 1);
  };

  const countNumber = (num) => {
    console.log("count number", num);
    for (let i = 0; i <= 100; i++) {}
    return num;
  };

  //   const checkData = useMemo(() => {
  //     return countNumber(myNum);
  //   }, [myNum]);

  const checkData = countNumber(myNum);

  return (
    <div className="memo-container">
      <button onClick={getValue} className="counter-button">
        Counter
      </button>
      <p className="number-display">My new number: {checkData}</p>
      <button onClick={() => setShow(!show)} className="toggle-button">
        {show ? "You clicked me" : "Click me, please"}
      </button>
    </div>
  );
};

export default Memo;
