/* Main Container */
.drag-drop-sticky-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* Header */
.drag-drop-sticky-container .table-header {
  text-align: center;
  margin-bottom: 30px;
  color: white;
}

.drag-drop-sticky-container .table-header h2 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.drag-drop-sticky-container .table-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Controls */
.drag-drop-sticky-container .table-controls {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  flex-wrap: wrap;
  gap: 20px;
}

.search-section {
  flex: 1;
  min-width: 300px;
}

.global-search {
  width: 100%;
  padding: 12px 20px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.sticky-controls {
  color: white;
  min-width: 300px;
}

.sticky-controls label {
  font-weight: 600;
  margin-bottom: 10px;
  display: block;
}

.sticky-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  max-height: 120px;
  overflow-y: auto;
}

.sticky-checkbox {
  display: flex;
  align-items: center;
  gap: 5px;
  background: rgba(255, 255, 255, 0.1);
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sticky-checkbox:hover {
  background: rgba(255, 255, 255, 0.2);
}

.sticky-checkbox input {
  margin: 0;
}

/* Table Wrapper */
.table-wrapper {
  background: white;
  border-radius: 20px;
  overflow: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  margin-bottom: 20px;
  max-height: 600px;
}

/* Table Styles */
.drag-drop-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  min-width: 1200px; /* Ensure horizontal scroll for many columns */
}

/* Draggable Header Styles */
.draggable-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  text-align: left;
  font-weight: 600;
  position: relative;
  user-select: none;
  border-right: 1px solid rgba(255,255,255,0.2);
}

.draggable-header.dragging {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow: 0 8px 25px rgba(0,0,0,0.3);
  transform: rotate(2deg);
}

.draggable-header.sortable .header-text {
  cursor: pointer;
}

.header-content {
  display: flex;
  align-items: stretch;
  min-height: 50px;
}

.drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  background: rgba(255,255,255,0.1);
  cursor: grab;
  border-right: 1px solid rgba(255,255,255,0.2);
  transition: background 0.2s ease;
}

.drag-handle:hover {
  background: rgba(255,255,255,0.2);
}

.drag-handle:active {
  cursor: grabbing;
}

.drag-icon {
  color: rgba(255,255,255,0.7);
  font-size: 12px;
  font-weight: bold;
  transform: rotate(90deg);
}

.header-text {
  flex: 1;
  padding: 15px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sort-indicator {
  font-size: 12px;
  margin-left: 5px;
}

.column-filter {
  width: calc(100% - 20px);
  margin: 0 10px 10px 10px;
  padding: 6px 8px;
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 8px;
  background: rgba(255,255,255,0.1);
  color: white;
  font-size: 12px;
}

.column-filter::placeholder {
  color: rgba(255,255,255,0.7);
}

/* Draggable Row Styles */
.draggable-row {
  transition: all 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
}

.draggable-row:hover {
  background-color: #f8f9ff;
}

.draggable-row.dragging {
  background-color: #e3f2fd !important;
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.table-cell {
  padding: 0;
  vertical-align: middle;
  position: relative;
  border-right: 1px solid #f0f0f0;
}

.sticky-cell {
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.row-drag-handle {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  cursor: grab;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10;
}

.draggable-row:hover .row-drag-handle {
  opacity: 1;
}

.row-drag-handle:active {
  cursor: grabbing;
}

.row-drag-handle .drag-icon {
  color: #667eea;
  font-size: 10px;
  font-weight: bold;
}

.cell-content {
  padding: 12px;
  min-height: 50px;
  display: flex;
  align-items: center;
}

/* Cell Styles */
.id-cell {
  font-weight: 600;
  color: #666;
  font-family: 'Courier New', monospace;
}

.avatar-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e0e0e0;
}

.name-cell {
  font-weight: 500;
  color: #333;
}

.email-cell {
  color: #1976d2;
  text-decoration: none;
  word-break: break-all;
}

.email-cell:hover {
  text-decoration: underline;
}

.phone-cell {
  font-family: 'Courier New', monospace;
  color: #666;
}

.department-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  white-space: nowrap;
}

.department-badge.engineering { background: #e3f2fd; color: #1976d2; }
.department-badge.marketing { background: #fce4ec; color: #c2185b; }
.department-badge.sales { background: #e8f5e8; color: #388e3c; }
.department-badge.hr { background: #fff3e0; color: #f57c00; }
.department-badge.finance { background: #f3e5f5; color: #7b1fa2; }
.department-badge.operations { background: #e0f2f1; color: #00695c; }

.position-cell {
  color: #555;
  font-style: italic;
}

.salary-cell {
  font-weight: 600;
  color: #2e7d32;
  font-family: 'Courier New', monospace;
}

.age-cell {
  text-align: center;
  font-weight: 500;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  white-space: nowrap;
}

.status-badge.active { background: #e8f5e8; color: #2e7d32; }
.status-badge.inactive { background: #ffebee; color: #c62828; }
.status-badge.on-leave { background: #fff3e0; color: #ef6c00; }

.performance-cell {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 120px;
}

.performance-bar {
  flex: 1;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.performance-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff4444, #ffaa00, #00aa00);
  transition: width 0.3s ease;
}

.performance-text {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  white-space: nowrap;
}

.actions-cell {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.edit-btn {
  background: #2196f3;
  color: white;
}

.edit-btn:hover {
  background: #1976d2;
  transform: translateY(-1px);
}

.delete-btn {
  background: #f44336;
  color: white;
}

.delete-btn:hover {
  background: #d32f2f;
  transform: translateY(-1px);
}

/* Pagination */
.drag-drop-sticky-container .table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  color: white;
}

.drag-drop-sticky-container .pagination-info {
  font-weight: 500;
}

.drag-drop-sticky-container .pagination-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.drag-drop-sticky-container .pagination-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.drag-drop-sticky-container .pagination-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.drag-drop-sticky-container .pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.drag-drop-sticky-container .page-info {
  margin: 0 15px;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .drag-drop-sticky-container .table-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-section,
  .sticky-controls {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .drag-drop-sticky-container {
    padding: 10px;
  }
  
  .drag-drop-sticky-container .table-header h2 {
    font-size: 1.8rem;
  }
  
  .sticky-checkboxes {
    max-height: 80px;
  }
  
  .drag-drop-table {
    font-size: 12px;
  }
  
  .cell-content {
    padding: 8px;
    min-height: 40px;
  }
}
