import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { flexRender } from '@tanstack/react-table';

const DraggableColumnHeader = ({ header, style }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: header.id,
  });

  const dragStyle = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    cursor: isDragging ? 'grabbing' : 'grab',
    ...style,
  };

  return (
    <th
      ref={setNodeRef}
      style={dragStyle}
      className={`draggable-header ${header.column.getCanSort() ? 'sortable' : ''} ${isDragging ? 'dragging' : ''}`}
      {...attributes}
    >
      <div className="header-content">
        <div className="drag-handle" {...listeners}>
          <span className="drag-icon">⋮⋮</span>
        </div>
        
        <div 
          className="header-text"
          onClick={header.column.getToggleSortingHandler()}
        >
          {flexRender(
            header.column.columnDef.header,
            header.getContext()
          )}
          {header.column.getCanSort() && (
            <span className="sort-indicator">
              {{
                asc: ' 🔼',
                desc: ' 🔽',
              }[header.column.getIsSorted()] ?? ' ↕️'}
            </span>
          )}
        </div>
      </div>
      
      {header.column.getCanFilter() && (
        <input
          type="text"
          placeholder={`Filter ${header.column.columnDef.header}...`}
          value={header.column.getFilterValue() ?? ''}
          onChange={(e) => header.column.setFilterValue(e.target.value)}
          className="column-filter"
          onClick={(e) => e.stopPropagation()}
        />
      )}
    </th>
  );
};

export default DraggableColumnHeader;
