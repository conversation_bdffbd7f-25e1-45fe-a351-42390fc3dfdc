import React, { useState } from "react";
import Memo from "./components/memo";
import YourComponent from "./slider";
import LayoutEffect from "./components/useLayoutEffect";
import UseIdHooks from "./components/useIdHooks";
import UseTransition from "./components/UseTransition";
import TanstackTableDemo from "./components/TanstackTableDemo";
import AdvancedTableFeatures from "./components/AdvancedTableFeatures";
import TanstackGuide from "./components/TanstackGuide";
import "./App.css";

const App = () => {
  const [activeDemo, setActiveDemo] = useState('basic');

  const demos = {
    basic: {
      title: 'Basic Table Demo',
      component: <TanstackTableDemo />
    },
    advanced: {
      title: 'Advanced Features',
      component: <AdvancedTableFeatures />
    },
    guide: {
      title: 'Learning Guide',
      component: <TanstackGuide />
    }
  };

  return (
    <div className="app">
      <nav className="app-nav">
        <div className="nav-container">
          <h1 className="app-title">🚀 Tanstack Table Mastery</h1>
          <div className="nav-buttons">
            {Object.entries(demos).map(([key, demo]) => (
              <button
                key={key}
                className={`nav-btn ${activeDemo === key ? 'active' : ''}`}
                onClick={() => setActiveDemo(key)}
              >
                {demo.title}
              </button>
            ))}
          </div>
        </div>
      </nav>

      <main className="app-main">
        {demos[activeDemo].component}
      </main>
    </div>
  );
};

export default App;
