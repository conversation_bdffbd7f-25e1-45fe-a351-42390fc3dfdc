import React from "react";
import Memo from "./components/memo";
import YourComponent from "./slider";
import LayoutEffect from "./components/useLayoutEffect";
import UseIdHooks from "./components/useIdHooks";
import UseTransition from "./components/UseTransition";

const App = () => {
  return (
    <div>
      {/* <Memo/> */}
      {/* <YourComponent/> */}
      <LayoutEffect />
      <h2>Choose password</h2>
      <UseIdHooks />
      <h2>Create password</h2>
      <UseIdHooks />
      <h2>Create password</h2>
      <UseIdHooks />
      <h2>Create password</h2>
      <UseIdHooks />

      <h2>UseTransition</h2>
      <UseTransition />
    </div>
  );
};

export default App;
