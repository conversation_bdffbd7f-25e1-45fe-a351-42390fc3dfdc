import React, { useState, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  flexRender,
} from '@tanstack/react-table';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  restrictToVerticalAxis,
  restrictToHorizontalAxis,
} from '@dnd-kit/modifiers';
import { sampleUsers } from '../data/dummyData';
import DraggableTableRow from './DraggableTableRow';
import DraggableColumnHeader from './DraggableColumnHeader';
import './DragDropStickyTable.css';

const DragDropStickyTable = () => {
  const [data, setData] = useState(sampleUsers);
  const [sorting, setSorting] = useState([]);
  const [filtering, setFiltering] = useState('');
  const [columnOrder, setColumnOrder] = useState([]);
  const [stickyColumns, setStickyColumns] = useState(['id', 'avatar', 'firstName']); // Multiple sticky columns

  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Define table columns with sticky configuration
  const columns = useMemo(
    () => [
      {
        accessorKey: 'id',
        header: 'ID',
        size: 80,
        cell: ({ getValue }) => (
          <span className="id-cell">{getValue()}</span>
        ),
        meta: {
          sticky: 'left',
          stickyOffset: 0,
        },
      },
      {
        accessorKey: 'avatar',
        header: 'Avatar',
        size: 100,
        cell: ({ getValue }) => (
          <img 
            src={getValue()} 
            alt="Avatar" 
            className="avatar-image"
          />
        ),
        enableSorting: false,
        meta: {
          sticky: 'left',
          stickyOffset: 80, // Offset by previous sticky column width
        },
      },
      {
        accessorKey: 'firstName',
        header: 'First Name',
        size: 150,
        cell: ({ getValue }) => (
          <span className="name-cell">{getValue()}</span>
        ),
        meta: {
          sticky: 'left',
          stickyOffset: 180, // Offset by previous sticky columns width
        },
      },
      {
        accessorKey: 'lastName',
        header: 'Last Name',
        size: 150,
        cell: ({ getValue }) => (
          <span className="name-cell">{getValue()}</span>
        ),
      },
      {
        accessorKey: 'email',
        header: 'Email',
        size: 250,
        cell: ({ getValue }) => (
          <a href={`mailto:${getValue()}`} className="email-cell">
            {getValue()}
          </a>
        ),
      },
      {
        accessorKey: 'phone',
        header: 'Phone',
        size: 150,
        cell: ({ getValue }) => (
          <span className="phone-cell">{getValue()}</span>
        ),
      },
      {
        accessorKey: 'department',
        header: 'Department',
        size: 130,
        cell: ({ getValue }) => (
          <span className={`department-badge ${getValue().toLowerCase()}`}>
            {getValue()}
          </span>
        ),
      },
      {
        accessorKey: 'position',
        header: 'Position',
        size: 200,
        cell: ({ getValue }) => (
          <span className="position-cell">{getValue()}</span>
        ),
      },
      {
        accessorKey: 'salary',
        header: 'Salary',
        size: 120,
        cell: ({ getValue }) => (
          <span className="salary-cell">
            ${getValue().toLocaleString()}
          </span>
        ),
      },
      {
        accessorKey: 'age',
        header: 'Age',
        size: 80,
        cell: ({ getValue }) => (
          <span className="age-cell">{getValue()}</span>
        ),
      },
      {
        accessorKey: 'status',
        header: 'Status',
        size: 120,
        cell: ({ getValue }) => (
          <span className={`status-badge ${getValue().toLowerCase().replace(' ', '-')}`}>
            {getValue()}
          </span>
        ),
      },
      {
        accessorKey: 'performance',
        header: 'Performance',
        size: 150,
        cell: ({ getValue }) => (
          <div className="performance-cell">
            <div className="performance-bar">
              <div 
                className="performance-fill" 
                style={{ width: `${(getValue() / 5) * 100}%` }}
              ></div>
            </div>
            <span className="performance-text">{getValue()}/5</span>
          </div>
        ),
      },
      {
        id: 'actions',
        header: 'Actions',
        size: 150,
        cell: ({ row }) => (
          <div className="actions-cell">
            <button 
              className="action-btn edit-btn"
              onClick={() => handleEdit(row.original)}
            >
              Edit
            </button>
            <button 
              className="action-btn delete-btn"
              onClick={() => handleDelete(row.original.id)}
            >
              Delete
            </button>
          </div>
        ),
        enableSorting: false,
        meta: {
          sticky: 'right',
          stickyOffset: 0,
        },
      },
    ],
    []
  );

  // Create table instance
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      globalFilter: filtering,
      columnOrder,
    },
    onSortingChange: setSorting,
    onGlobalFilterChange: setFiltering,
    onColumnOrderChange: setColumnOrder,
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  });

  // Handle column drag end
  const handleColumnDragEnd = (event) => {
    const { active, over } = event;
    
    if (active.id !== over?.id) {
      const oldIndex = columnOrder.indexOf(active.id);
      const newIndex = columnOrder.indexOf(over.id);
      
      if (oldIndex !== -1 && newIndex !== -1) {
        setColumnOrder(arrayMove(columnOrder, oldIndex, newIndex));
      }
    }
  };

  // Handle row drag end
  const handleRowDragEnd = (event) => {
    const { active, over } = event;
    
    if (active.id !== over?.id) {
      setData((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id);
        const newIndex = items.findIndex(item => item.id === over.id);
        
        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  // Action handlers
  const handleEdit = (user) => {
    alert(`Edit user: ${user.firstName} ${user.lastName}`);
  };

  const handleDelete = (userId) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      setData(prev => prev.filter(user => user.id !== userId));
    }
  };

  // Toggle sticky column
  const toggleStickyColumn = (columnId) => {
    setStickyColumns(prev => {
      if (prev.includes(columnId)) {
        return prev.filter(id => id !== columnId);
      } else {
        return [...prev, columnId];
      }
    });
  };

  // Get column style for sticky positioning
  const getColumnStyle = (column) => {
    const columnMeta = column.columnDef.meta;
    if (!columnMeta?.sticky || !stickyColumns.includes(column.id)) {
      return {};
    }

    const style = {
      position: 'sticky',
      zIndex: 10,
      backgroundColor: 'white',
    };

    if (columnMeta.sticky === 'left') {
      style.left = `${columnMeta.stickyOffset}px`;
      style.borderRight = '2px solid #e0e0e0';
    } else if (columnMeta.sticky === 'right') {
      style.right = `${columnMeta.stickyOffset}px`;
      style.borderLeft = '2px solid #e0e0e0';
    }

    return style;
  };

  return (
    <div className="drag-drop-sticky-container">
      <div className="table-header">
        <h2>🎯 Advanced Table: Drag & Drop + Sticky Columns</h2>
        <p>Drag columns to reorder • Drag rows to reorder • Multiple sticky columns</p>
      </div>

      <div className="table-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder="Search all columns..."
            value={filtering}
            onChange={(e) => setFiltering(e.target.value)}
            className="global-search"
          />
        </div>
        
        <div className="sticky-controls">
          <label>Sticky Columns:</label>
          <div className="sticky-checkboxes">
            {table.getAllColumns().map(column => (
              <label key={column.id} className="sticky-checkbox">
                <input
                  type="checkbox"
                  checked={stickyColumns.includes(column.id)}
                  onChange={() => toggleStickyColumn(column.id)}
                />
                {column.columnDef.header}
              </label>
            ))}
          </div>
        </div>
      </div>

      <div className="table-wrapper">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleColumnDragEnd}
          modifiers={[restrictToHorizontalAxis]}
        >
          <table className="drag-drop-table">
            <thead>
              <SortableContext
                items={table.getAllColumns().map(c => c.id)}
                strategy={horizontalListSortingStrategy}
              >
                {table.getHeaderGroups().map(headerGroup => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <DraggableColumnHeader
                        key={header.id}
                        header={header}
                        style={getColumnStyle(header.column)}
                      />
                    ))}
                  </tr>
                ))}
              </SortableContext>
            </thead>
          </table>
        </DndContext>

        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleRowDragEnd}
          modifiers={[restrictToVerticalAxis]}
        >
          <table className="drag-drop-table">
            <tbody>
              <SortableContext
                items={table.getRowModel().rows.map(row => row.original.id)}
                strategy={verticalListSortingStrategy}
              >
                {table.getRowModel().rows.map(row => (
                  <DraggableTableRow
                    key={row.id}
                    row={row}
                    getColumnStyle={getColumnStyle}
                  />
                ))}
              </SortableContext>
            </tbody>
          </table>
        </DndContext>
      </div>

      <div className="table-pagination">
        <div className="pagination-info">
          <span>
            Showing {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} to{' '}
            {Math.min(
              (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
              table.getFilteredRowModel().rows.length
            )}{' '}
            of {table.getFilteredRowModel().rows.length} entries
          </span>
        </div>
        
        <div className="pagination-controls">
          <button
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
            className="pagination-btn"
          >
            {'<<'}
          </button>
          <button
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="pagination-btn"
          >
            {'<'}
          </button>
          
          <span className="page-info">
            Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
          </span>
          
          <button
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="pagination-btn"
          >
            {'>'}
          </button>
          <button
            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage()}
            className="pagination-btn"
          >
            {'>>'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DragDropStickyTable;
