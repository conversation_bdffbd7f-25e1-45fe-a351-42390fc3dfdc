/* Guide Container */
.tanstack-guide {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.guide-header {
  text-align: center;
  padding: 40px 20px;
  color: white;
}

.guide-header h1 {
  font-size: 3rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.guide-header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* Layout */
.guide-layout {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  background: white;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
  box-shadow: 0 -10px 30px rgba(0,0,0,0.2);
  min-height: calc(100vh - 200px);
}

/* Navigation */
.guide-nav {
  width: 280px;
  background: #f8f9fa;
  padding: 30px 0;
  border-right: 1px solid #e9ecef;
}

.nav-item {
  display: block;
  width: 100%;
  padding: 15px 30px;
  border: none;
  background: none;
  text-align: left;
  font-size: 16px;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 4px solid transparent;
}

.nav-item:hover {
  background: #e9ecef;
  color: #212529;
}

.nav-item.active {
  background: #e3f2fd;
  color: #1976d2;
  border-left-color: #1976d2;
  font-weight: 600;
}

/* Main Content */
.guide-main {
  flex: 1;
  padding: 40px;
  overflow-y: auto;
}

.section-content h2 {
  color: #212529;
  margin-bottom: 30px;
  font-size: 2.2rem;
  border-bottom: 3px solid #1976d2;
  padding-bottom: 10px;
}

.guide-content h3 {
  color: #495057;
  margin: 30px 0 15px 0;
  font-size: 1.5rem;
}

.guide-content h4 {
  color: #6c757d;
  margin: 20px 0 10px 0;
  font-size: 1.2rem;
}

.guide-content h5 {
  color: #495057;
  margin: 15px 0 8px 0;
  font-size: 1rem;
}

.guide-content p {
  line-height: 1.6;
  color: #495057;
  margin-bottom: 15px;
}

.guide-content ul {
  margin: 15px 0;
  padding-left: 20px;
}

.guide-content li {
  margin-bottom: 8px;
  line-height: 1.5;
  color: #495057;
}

/* Concept Grid */
.concept-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.concept-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #1976d2;
  transition: transform 0.2s ease;
}

.concept-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.concept-card h5 {
  color: #1976d2;
  margin-bottom: 10px;
  font-weight: 600;
}

.concept-card p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* Code Blocks */
.code-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.code-block code {
  font-size: 14px;
  line-height: 1.4;
}

.code-block pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.mini-code {
  background: #f1f3f4;
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 10px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #1976d2;
  border: 1px solid #e0e0e0;
}

/* Property List */
.property-list {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.property-item {
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
  line-height: 1.5;
}

.property-item:last-child {
  border-bottom: none;
}

.property-item strong {
  color: #1976d2;
  font-weight: 600;
}

/* Feature Grid */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.feature-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 25px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  border-color: #1976d2;
}

.feature-card h4 {
  color: #1976d2;
  margin-bottom: 15px;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-card p {
  margin-bottom: 15px;
  color: #6c757d;
  font-size: 14px;
}

/* Tip List */
.tip-list {
  margin: 20px 0;
}

.tip-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  border-left: 4px solid #28a745;
}

.tip-item h4 {
  color: #28a745;
  margin-bottom: 10px;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tip-item p {
  margin-bottom: 10px;
  color: #495057;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .guide-layout {
    flex-direction: column;
  }
  
  .guide-nav {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding: 20px 0;
  }
  
  .nav-item {
    white-space: nowrap;
    border-left: none;
    border-bottom: 4px solid transparent;
    min-width: 150px;
  }
  
  .nav-item.active {
    border-left: none;
    border-bottom-color: #1976d2;
  }
  
  .guide-main {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .guide-header h1 {
    font-size: 2rem;
  }
  
  .guide-header p {
    font-size: 1rem;
  }
  
  .section-content h2 {
    font-size: 1.8rem;
  }
  
  .concept-grid,
  .feature-grid {
    grid-template-columns: 1fr;
  }
  
  .code-block {
    padding: 15px;
    font-size: 12px;
  }
  
  .guide-main {
    padding: 15px;
  }
}

/* Scrollbar Styling */
.guide-main::-webkit-scrollbar {
  width: 8px;
}

.guide-main::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.guide-main::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.guide-main::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
