import React, { useState } from 'react';
import './TanstackGuide.css';

const TanstackGuide = () => {
  const [activeSection, setActiveSection] = useState('overview');

  const sections = {
    overview: {
      title: 'Tanstack Table Overview',
      content: (
        <div className="guide-content">
          <h3>What is Tanstack Table?</h3>
          <p>
            Tanstack Table (formerly React Table) is a headless UI library for building powerful tables and datagrids. 
            It's framework-agnostic and provides the logic without any UI components, giving you complete control over markup and styling.
          </p>
          
          <h4>Key Features:</h4>
          <ul>
            <li>🔄 <strong>Headless</strong> - No UI components, just logic</li>
            <li>⚡ <strong>Lightweight</strong> - 12kb gzipped</li>
            <li>🎯 <strong>TypeScript</strong> - First-class TypeScript support</li>
            <li>🔧 <strong>Extensible</strong> - Plugin-based architecture</li>
            <li>📱 <strong>Framework Agnostic</strong> - Works with <PERSON><PERSON>, Vue, Solid, etc.</li>
          </ul>

          <h4>Core Concepts:</h4>
          <div className="concept-grid">
            <div className="concept-card">
              <h5>Table Instance</h5>
              <p>The main table object created with useReactTable()</p>
            </div>
            <div className="concept-card">
              <h5>Columns</h5>
              <p>Define structure, behavior, and rendering of table columns</p>
            </div>
            <div className="concept-card">
              <h5>Rows</h5>
              <p>Data rows with methods for selection, expansion, etc.</p>
            </div>
            <div className="concept-card">
              <h5>Cells</h5>
              <p>Individual data cells with custom rendering capabilities</p>
            </div>
          </div>
        </div>
      )
    },
    setup: {
      title: 'Basic Setup',
      content: (
        <div className="guide-content">
          <h3>Installation</h3>
          <div className="code-block">
            <code>npm install @tanstack/react-table</code>
          </div>

          <h3>Basic Implementation</h3>
          <div className="code-block">
            <pre>{`import { useReactTable, getCoreRowModel, flexRender } from '@tanstack/react-table'

const MyTable = () => {
  const data = [
    { id: 1, name: 'John', age: 30 },
    { id: 2, name: 'Jane', age: 25 }
  ]

  const columns = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'age',
      header: 'Age',
    }
  ]

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  return (
    <table>
      <thead>
        {table.getHeaderGroups().map(headerGroup => (
          <tr key={headerGroup.id}>
            {headerGroup.headers.map(header => (
              <th key={header.id}>
                {flexRender(header.column.columnDef.header, header.getContext())}
              </th>
            ))}
          </tr>
        ))}
      </thead>
      <tbody>
        {table.getRowModel().rows.map(row => (
          <tr key={row.id}>
            {row.getVisibleCells().map(cell => (
              <td key={cell.id}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  )
}`}</pre>
          </div>
        </div>
      )
    },
    columns: {
      title: 'Column Configuration',
      content: (
        <div className="guide-content">
          <h3>Column Definition Options</h3>
          
          <h4>Basic Column Properties:</h4>
          <div className="property-list">
            <div className="property-item">
              <strong>accessorKey:</strong> The key to access data from row object
            </div>
            <div className="property-item">
              <strong>header:</strong> Column header content (string or function)
            </div>
            <div className="property-item">
              <strong>cell:</strong> Custom cell renderer function
            </div>
            <div className="property-item">
              <strong>size:</strong> Column width (number)
            </div>
            <div className="property-item">
              <strong>enableSorting:</strong> Enable/disable sorting for this column
            </div>
            <div className="property-item">
              <strong>enableColumnFilter:</strong> Enable/disable filtering for this column
            </div>
          </div>

          <h4>Advanced Column Examples:</h4>
          <div className="code-block">
            <pre>{`const columns = [
  {
    accessorKey: 'avatar',
    header: 'Photo',
    cell: ({ getValue }) => (
      <img src={getValue()} alt="Avatar" style={{ width: 40, height: 40 }} />
    ),
    enableSorting: false,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ getValue }) => (
      <span className={\`status-\${getValue().toLowerCase()}\`}>
        {getValue()}
      </span>
    ),
    filterFn: 'equals',
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => (
      <button onClick={() => handleEdit(row.original)}>
        Edit
      </button>
    ),
    enableSorting: false,
  }
]`}</pre>
          </div>
        </div>
      )
    },
    features: {
      title: 'Core Features',
      content: (
        <div className="guide-content">
          <h3>Essential Table Features</h3>
          
          <div className="feature-grid">
            <div className="feature-card">
              <h4>🔍 Filtering</h4>
              <p>Global and column-specific filtering with custom filter functions</p>
              <div className="mini-code">
                <code>getFilteredRowModel: getFilteredRowModel()</code>
              </div>
            </div>
            
            <div className="feature-card">
              <h4>🔄 Sorting</h4>
              <p>Multi-column sorting with custom sorting functions</p>
              <div className="mini-code">
                <code>getSortedRowModel: getSortedRowModel()</code>
              </div>
            </div>
            
            <div className="feature-card">
              <h4>📄 Pagination</h4>
              <p>Built-in pagination with customizable page sizes</p>
              <div className="mini-code">
                <code>getPaginationRowModel: getPaginationRowModel()</code>
              </div>
            </div>
            
            <div className="feature-card">
              <h4>✅ Row Selection</h4>
              <p>Single and multi-row selection with bulk actions</p>
              <div className="mini-code">
                <code>enableRowSelection: true</code>
              </div>
            </div>
            
            <div className="feature-card">
              <h4>📊 Grouping</h4>
              <p>Group rows by column values with aggregation</p>
              <div className="mini-code">
                <code>getGroupedRowModel: getGroupedRowModel()</code>
              </div>
            </div>
            
            <div className="feature-card">
              <h4>🔽 Expansion</h4>
              <p>Expandable rows for detailed views</p>
              <div className="mini-code">
                <code>getExpandedRowModel: getExpandedRowModel()</code>
              </div>
            </div>
          </div>

          <h3>State Management</h3>
          <p>Tanstack Table uses controlled state for all features:</p>
          <div className="code-block">
            <pre>{`const [sorting, setSorting] = useState([])
const [filtering, setFiltering] = useState('')
const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 })

const table = useReactTable({
  data,
  columns,
  state: {
    sorting,
    globalFilter: filtering,
    pagination,
  },
  onSortingChange: setSorting,
  onGlobalFilterChange: setFiltering,
  onPaginationChange: setPagination,
  // ... other options
})`}</pre>
          </div>
        </div>
      )
    },
    styling: {
      title: 'Styling & Customization',
      content: (
        <div className="guide-content">
          <h3>Styling Approaches</h3>
          
          <h4>1. CSS Classes</h4>
          <p>Apply CSS classes to table elements:</p>
          <div className="code-block">
            <pre>{`<table className="custom-table">
  <thead className="table-header">
    <tr className="header-row">
      <th className="header-cell sortable">Name</th>
    </tr>
  </thead>
</table>`}</pre>
          </div>

          <h4>2. Inline Styles</h4>
          <p>Use inline styles for dynamic styling:</p>
          <div className="code-block">
            <pre>{`<th 
  style={{ 
    width: header.getSize(),
    backgroundColor: header.column.getIsSorted() ? '#f0f0f0' : 'white'
  }}
>
  {header.column.columnDef.header}
</th>`}</pre>
          </div>

          <h4>3. CSS-in-JS</h4>
          <p>Use styled-components or emotion for component-scoped styles:</p>
          <div className="code-block">
            <pre>{`const StyledTable = styled.table\`
  border-collapse: collapse;
  width: 100%;
  
  th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px;
  }
  
  tr:hover {
    background-color: #f5f5f5;
  }
\``}</pre>
          </div>

          <h4>4. Conditional Styling</h4>
          <p>Apply styles based on data or state:</p>
          <div className="code-block">
            <pre>{`cell: ({ getValue, row }) => (
  <span 
    className={\`status-badge \${getValue().toLowerCase()}\`}
    style={{
      backgroundColor: row.getIsSelected() ? '#e3f2fd' : 'transparent'
    }}
  >
    {getValue()}
  </span>
)`}</pre>
          </div>
        </div>
      )
    },
    performance: {
      title: 'Performance Tips',
      content: (
        <div className="guide-content">
          <h3>Optimization Strategies</h3>
          
          <div className="tip-list">
            <div className="tip-item">
              <h4>🎯 Memoize Columns</h4>
              <p>Use useMemo for column definitions to prevent unnecessary re-renders:</p>
              <div className="mini-code">
                <code>const columns = useMemo(() => [...], [])</code>
              </div>
            </div>
            
            <div className="tip-item">
              <h4>📊 Virtual Scrolling</h4>
              <p>For large datasets, implement virtual scrolling with @tanstack/react-virtual</p>
            </div>
            
            <div className="tip-item">
              <h4>🔄 Server-Side Operations</h4>
              <p>Move sorting, filtering, and pagination to the server for large datasets</p>
            </div>
            
            <div className="tip-item">
              <h4>⚡ Debounce Filters</h4>
              <p>Debounce filter inputs to reduce API calls and improve performance</p>
            </div>
            
            <div className="tip-item">
              <h4>🎨 Optimize Renders</h4>
              <p>Use React.memo for custom cell components that don't need frequent updates</p>
            </div>
          </div>

          <h3>Memory Management</h3>
          <div className="code-block">
            <pre>{`// Good: Memoized columns
const columns = useMemo(() => [
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ getValue }) => <span>{getValue()}</span>
  }
], [])

// Bad: Recreated on every render
const columns = [
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ getValue }) => <span>{getValue()}</span>
  }
]`}</pre>
          </div>
        </div>
      )
    }
  };

  return (
    <div className="tanstack-guide">
      <div className="guide-header">
        <h1>📚 Tanstack Table Learning Guide</h1>
        <p>Complete guide to mastering Tanstack Table</p>
      </div>

      <div className="guide-layout">
        <nav className="guide-nav">
          {Object.entries(sections).map(([key, section]) => (
            <button
              key={key}
              className={`nav-item ${activeSection === key ? 'active' : ''}`}
              onClick={() => setActiveSection(key)}
            >
              {section.title}
            </button>
          ))}
        </nav>

        <main className="guide-main">
          <div className="section-content">
            <h2>{sections[activeSection].title}</h2>
            {sections[activeSection].content}
          </div>
        </main>
      </div>
    </div>
  );
};

export default TanstackGuide;
