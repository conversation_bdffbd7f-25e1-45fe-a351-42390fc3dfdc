/* Main Container */
.tanstack-table-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* Header Section */
.table-header {
  text-align: center;
  margin-bottom: 30px;
  color: white;
}

.table-header h2 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.table-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Controls Section */
.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.search-section {
  flex: 1;
  margin-right: 20px;
}

.global-search {
  width: 100%;
  max-width: 400px;
  padding: 12px 20px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.global-search:focus {
  outline: none;
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.action-section {
  display: flex;
  gap: 15px;
  align-items: center;
}

.add-btn {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.page-size-select {
  padding: 10px 15px;
  border: none;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  font-weight: 500;
}

/* Table Wrapper */
.table-wrapper {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  margin-bottom: 20px;
}

/* Table Styles */
.custom-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.custom-table thead {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.custom-table th {
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  position: relative;
}

.custom-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sort-indicator {
  font-size: 12px;
  margin-left: 5px;
}

.column-filter {
  width: 100%;
  padding: 6px 8px;
  margin-top: 8px;
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 8px;
  background: rgba(255,255,255,0.1);
  color: white;
  font-size: 12px;
}

.column-filter::placeholder {
  color: rgba(255,255,255,0.7);
}

.custom-table td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
}

.table-row:hover {
  background-color: #f8f9ff;
  transform: scale(1.01);
  transition: all 0.2s ease;
}

/* Cell Styles */
.id-cell {
  font-weight: 600;
  color: #666;
}

.avatar-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e0e0e0;
}

.name-cell {
  font-weight: 500;
  color: #333;
}

.email-cell {
  color: #1976d2;
  text-decoration: none;
}

.email-cell:hover {
  text-decoration: underline;
}

.department-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.department-badge.engineering { background: #e3f2fd; color: #1976d2; }
.department-badge.marketing { background: #fce4ec; color: #c2185b; }
.department-badge.sales { background: #e8f5e8; color: #388e3c; }
.department-badge.hr { background: #fff3e0; color: #f57c00; }
.department-badge.finance { background: #f3e5f5; color: #7b1fa2; }
.department-badge.operations { background: #e0f2f1; color: #00695c; }

.position-cell {
  color: #555;
  font-style: italic;
}

.salary-cell {
  font-weight: 600;
  color: #2e7d32;
}

.age-cell {
  text-align: center;
  font-weight: 500;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.active { background: #e8f5e8; color: #2e7d32; }
.status-badge.inactive { background: #ffebee; color: #c62828; }
.status-badge.on-leave { background: #fff3e0; color: #ef6c00; }

.performance-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.performance-bar {
  flex: 1;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.performance-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff4444, #ffaa00, #00aa00);
  transition: width 0.3s ease;
}

.performance-text {
  font-size: 12px;
  font-weight: 600;
  color: #666;
}

.actions-cell {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.edit-btn {
  background: #2196f3;
  color: white;
}

.edit-btn:hover {
  background: #1976d2;
  transform: translateY(-1px);
}

.delete-btn {
  background: #f44336;
  color: white;
}

.delete-btn:hover {
  background: #d32f2f;
  transform: translateY(-1px);
}

/* Pagination */
.table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  color: white;
}

.pagination-info {
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pagination-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  margin: 0 15px;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .custom-table {
    font-size: 12px;
  }
  
  .custom-table th,
  .custom-table td {
    padding: 8px;
  }
}

@media (max-width: 768px) {
  .table-controls {
    flex-direction: column;
    gap: 15px;
  }
  
  .search-section {
    margin-right: 0;
  }
  
  .table-pagination {
    flex-direction: column;
    gap: 15px;
  }
  
  .custom-table {
    font-size: 11px;
  }
}
