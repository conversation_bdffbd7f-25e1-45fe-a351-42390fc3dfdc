import faker from 'faker';

// Generate dummy user data
export const generateUsers = (count = 100) => {
  const users = [];
  
  for (let i = 0; i < count; i++) {
    users.push({
      id: i + 1,
      firstName: faker.name.firstName(),
      lastName: faker.name.lastName(),
      email: faker.internet.email(),
      phone: faker.phone.phoneNumber(),
      age: faker.datatype.number({ min: 18, max: 80 }),
      department: faker.random.arrayElement([
        'Engineering',
        'Marketing',
        'Sales',
        'HR',
        'Finance',
        'Operations'
      ]),
      position: faker.name.jobTitle(),
      salary: faker.datatype.number({ min: 30000, max: 150000 }),
      joinDate: faker.date.between('2020-01-01', '2024-01-01').toISOString().split('T')[0],
      status: faker.random.arrayElement(['Active', 'Inactive', 'On Leave']),
      avatar: faker.image.avatar(),
      address: {
        street: faker.address.streetAddress(),
        city: faker.address.city(),
        state: faker.address.state(),
        zipCode: faker.address.zipCode(),
        country: faker.address.country()
      },
      skills: faker.random.arrayElements([
        'JavaScript', 'React', 'Node.js', 'Python', 'Java',
        'SQL', 'MongoDB', 'AWS', 'Docker', 'Git',
        'Marketing', 'Sales', 'Communication', 'Leadership'
      ], faker.datatype.number({ min: 2, max: 6 })),
      performance: parseFloat(faker.datatype.float({ min: 1, max: 5, precision: 0.1 }).toFixed(1)),
      lastLogin: faker.date.recent(30).toISOString(),
    });
  }
  
  return users;
};

// Generate dummy product data
export const generateProducts = (count = 50) => {
  const products = [];
  
  for (let i = 0; i < count; i++) {
    products.push({
      id: i + 1,
      name: faker.commerce.productName(),
      category: faker.commerce.department(),
      price: parseFloat(faker.commerce.price(10, 1000)),
      stock: faker.datatype.number({ min: 0, max: 500 }),
      description: faker.commerce.productDescription(),
      brand: faker.company.companyName(),
      rating: parseFloat(faker.datatype.float({ min: 1, max: 5, precision: 0.1 }).toFixed(1)),
      reviews: faker.datatype.number({ min: 0, max: 1000 }),
      image: faker.image.imageUrl(200, 200, 'product'),
      tags: faker.random.arrayElements([
        'Popular', 'New', 'Sale', 'Limited', 'Premium', 'Eco-friendly'
      ], faker.datatype.number({ min: 0, max: 3 })),
      createdAt: faker.date.past(2).toISOString().split('T')[0],
      updatedAt: faker.date.recent(30).toISOString().split('T')[0],
    });
  }
  
  return products;
};

// Generate dummy order data
export const generateOrders = (count = 200) => {
  const orders = [];
  
  for (let i = 0; i < count; i++) {
    const orderDate = faker.date.past(1);
    orders.push({
      id: `ORD-${String(i + 1).padStart(6, '0')}`,
      customerName: faker.name.findName(),
      customerEmail: faker.internet.email(),
      orderDate: orderDate.toISOString().split('T')[0],
      deliveryDate: faker.date.future(0.1, orderDate).toISOString().split('T')[0],
      status: faker.random.arrayElement([
        'Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled'
      ]),
      total: parseFloat(faker.datatype.float({ min: 25, max: 2000, precision: 0.01 }).toFixed(2)),
      items: faker.datatype.number({ min: 1, max: 10 }),
      paymentMethod: faker.random.arrayElement([
        'Credit Card', 'PayPal', 'Bank Transfer', 'Cash on Delivery'
      ]),
      shippingAddress: {
        street: faker.address.streetAddress(),
        city: faker.address.city(),
        state: faker.address.state(),
        zipCode: faker.address.zipCode(),
        country: faker.address.country()
      },
      priority: faker.random.arrayElement(['Low', 'Medium', 'High', 'Urgent']),
    });
  }
  
  return orders;
};

// Sample data for quick testing
export const sampleUsers = generateUsers(20);
export const sampleProducts = generateProducts(15);
export const sampleOrders = generateOrders(30);
