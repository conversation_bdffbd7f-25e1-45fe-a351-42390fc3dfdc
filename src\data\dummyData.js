import { faker } from '@faker-js/faker';

// Generate dummy user data
export const generateUsers = (count = 100) => {
  const users = [];
  
  for (let i = 0; i < count; i++) {
    users.push({
      id: i + 1,
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      phone: faker.phone.number(),
      age: faker.number.int({ min: 18, max: 80 }),
      department: faker.helpers.arrayElement([
        'Engineering', 
        'Marketing', 
        'Sales', 
        'HR', 
        'Finance', 
        'Operations'
      ]),
      position: faker.person.jobTitle(),
      salary: faker.number.int({ min: 30000, max: 150000 }),
      joinDate: faker.date.between({ 
        from: '2020-01-01', 
        to: '2024-01-01' 
      }).toISOString().split('T')[0],
      status: faker.helpers.arrayElement(['Active', 'Inactive', 'On Leave']),
      avatar: faker.image.avatar(),
      address: {
        street: faker.location.streetAddress(),
        city: faker.location.city(),
        state: faker.location.state(),
        zipCode: faker.location.zipCode(),
        country: faker.location.country()
      },
      skills: faker.helpers.arrayElements([
        'JavaScript', 'React', 'Node.js', 'Python', 'Java', 
        'SQL', 'MongoDB', 'AWS', 'Docker', 'Git',
        'Marketing', 'Sales', 'Communication', 'Leadership'
      ], { min: 2, max: 6 }),
      performance: faker.number.float({ min: 1, max: 5, precision: 0.1 }),
      lastLogin: faker.date.recent({ days: 30 }).toISOString(),
    });
  }
  
  return users;
};

// Generate dummy product data
export const generateProducts = (count = 50) => {
  const products = [];
  
  for (let i = 0; i < count; i++) {
    products.push({
      id: i + 1,
      name: faker.commerce.productName(),
      category: faker.commerce.department(),
      price: parseFloat(faker.commerce.price({ min: 10, max: 1000 })),
      stock: faker.number.int({ min: 0, max: 500 }),
      description: faker.commerce.productDescription(),
      brand: faker.company.name(),
      rating: faker.number.float({ min: 1, max: 5, precision: 0.1 }),
      reviews: faker.number.int({ min: 0, max: 1000 }),
      image: faker.image.url({ width: 200, height: 200 }),
      tags: faker.helpers.arrayElements([
        'Popular', 'New', 'Sale', 'Limited', 'Premium', 'Eco-friendly'
      ], { min: 0, max: 3 }),
      createdAt: faker.date.past({ years: 2 }).toISOString().split('T')[0],
      updatedAt: faker.date.recent({ days: 30 }).toISOString().split('T')[0],
    });
  }
  
  return products;
};

// Generate dummy order data
export const generateOrders = (count = 200) => {
  const orders = [];
  
  for (let i = 0; i < count; i++) {
    const orderDate = faker.date.past({ years: 1 });
    orders.push({
      id: `ORD-${String(i + 1).padStart(6, '0')}`,
      customerName: faker.person.fullName(),
      customerEmail: faker.internet.email(),
      orderDate: orderDate.toISOString().split('T')[0],
      deliveryDate: faker.date.future({ 
        years: 0.1, 
        refDate: orderDate 
      }).toISOString().split('T')[0],
      status: faker.helpers.arrayElement([
        'Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled'
      ]),
      total: faker.number.float({ min: 25, max: 2000, precision: 0.01 }),
      items: faker.number.int({ min: 1, max: 10 }),
      paymentMethod: faker.helpers.arrayElement([
        'Credit Card', 'PayPal', 'Bank Transfer', 'Cash on Delivery'
      ]),
      shippingAddress: {
        street: faker.location.streetAddress(),
        city: faker.location.city(),
        state: faker.location.state(),
        zipCode: faker.location.zipCode(),
        country: faker.location.country()
      },
      priority: faker.helpers.arrayElement(['Low', 'Medium', 'High', 'Urgent']),
    });
  }
  
  return orders;
};

// Sample data for quick testing
export const sampleUsers = generateUsers(20);
export const sampleProducts = generateProducts(15);
export const sampleOrders = generateOrders(30);
